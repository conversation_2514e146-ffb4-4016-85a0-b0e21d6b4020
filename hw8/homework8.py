############################################################
# CIS 521: Homework 8
############################################################

############################################################
# Imports
import string
############################################################

# Include your imports here, if any are used.

############################################################

student_name = "<PERSON> Richards"

############################################################
# Section 1: Ngram Models
############################################################


def tokenize(text):
    tokens = []
    for word in text.split():
        contains_punctuation = False
        temp_token = ""
        for character in word:
            if character in string.punctuation:
                contains_punctuation = True
                if temp_token:
                    tokens.append(temp_token)
                tokens.append(character)
                temp_token = ""
            else:
                temp_token += character
        if not contains_punctuation:
            if word:
                tokens.append(word)
    return tokens


def ngrams(n, tokens):
    """
    Produces a list of all n-grams of the specified size from the input token list.

    Args:
        n: Size of n-grams (must be >= 1)
        tokens: List of input tokens

    Returns:
        List of tuples (context, token) where context is an (n-1)-element tuple
    """
    # Add padding: n-1 "<START>" tokens at beginning, 1 "<END>" token at end
    padded_tokens = ["<START>"] * (n - 1) + tokens + ["<END>"]

    result = []

    # Generate n-grams
    for i in range(len(padded_tokens) - n + 1):
        # Extract context (n-1 preceding tokens) and current token
        context = tuple(padded_tokens[i : i + n - 1])
        token = padded_tokens[i + n - 1]
        result.append((context, token))

    return result


class NgramModel(object):

    def __init__(self, n):
        # TODO
        self.n = n

    def update(self, sentence):
        pass

    def prob(self, context, token):
        pass

    def random_token(self, context):
        pass

    def random_text(self, token_count):
        pass

    def perplexity(self, sentence):
        pass


def create_ngram_model(n, path):
    pass

############################################################
# Section 2: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""

feedback_question_2 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""

feedback_question_3 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""

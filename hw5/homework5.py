############################################################
# Imports
############################################################

# Include your imports here, if any are used.

import collections
import copy
import itertools
import random
import math


############################################################
# CIS 521: Homework 5
############################################################

student_name = "Valencia Richards"


############################################################
# Sudoku Solver
############################################################


def sudoku_cells():
    pass


def sudoku_arcs():
    pass


def read_board(path):
    board = dict()
    with open(path, 'r') as file_handler:
        for row_number, line in enumerate(file_handler):
            board[row_number] = dict()
            for col_number, char in enumerate(line):
                if char == '*':
                    board[line_number][col_number] = set(range(1, 10))
                else:
                    number = int(char)
                    board[line_number][col_number] = {number}
            line_number += 1
        return board


class Sudoku(object):

    CELLS = sudoku_cells()
    ARCS = sudoku_arcs()

    def __init__(self, board):
        self.board = board

    def get_values(self, cell):
        return self.board.get(cell[0], {}).get(cell[1], set())

    def remove_inconsistent_values(self, cell1, cell2):
        pass

    def infer_ac3(self):
        pass

    def infer_improved(self):
        pass

    def infer_with_guessing(self):
        pass

############################################################
# Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent about 1 hour on this assignment.
(I have had a really, really challenging past few weeks.)
"""

feedback_question_2 = """
My biggest challenge was lack of time, due to exam prep and
other personal and medical issues.
"""

feedback_question_3 = """
The theory was very interesting.
"""
